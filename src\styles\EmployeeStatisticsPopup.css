@import url('../index.css');
/* Overlay */
.employee-statistics-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #CCCCCC66;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* Popup Container */
.employee-statistics-popup {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 860px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

/* Header */
.employee-statistics-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px 24px 0 24px;
  padding-bottom: 20px;
}

.employee-statistics-avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.employee-statistics-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.employee-statistics-avatar img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.employee-statistics-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.employee-statistics-name {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.2;
}

.employee-statistics-position {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.employee-statistics-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.employee-statistics-close-btn:hover {
  background-color: #f5f5f5;
}

.employee-statistics-close-btn img {
  width: 20px;
  height: 20px;
  opacity: 0.6;
}

/* Metrics Section */
.employee-statistics-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  padding: 20px 24px;
  margin: 0;
  border-radius: 12px;
}

.employee-statistics-metric-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #7C7C7C1A;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* .employee-statistics-metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
} */

.employee-statistics-metric-icon {
  width: 24px;
  height: 24px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
}

.employee-statistics-metric-icon img {
  width: 22px;
  height: 22px;
  filter: invert(37%) sepia(0%) saturate(1391%) hue-rotate(231deg) brightness(94%) contrast(95%);
}

.employee-statistics-metric-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.employee-statistics-metric-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
}

.employee-statistics-metric-label {
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 600;
  line-height: 1.2;
}

.employee-statistics-metric-value {
  font-size: 22px;
  font-weight: 600;
  color: #5B5B5B;
  line-height: 1.2;
}

/* Tabs */
.employee-statistics-tabs {
  display: flex;
  gap: 8px;
  margin: 0 24px;
  margin-top: 24px;
  padding: 4px;
  background: #f5f5f5;
  border-radius: 8px;
}

.employee-statistics-tab {
  background: transparent;
  border: none;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  color: #5b5b5b;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s;
  position: relative;
  flex: 1;
  text-align: center;
}
.employee-statistics-tab.active {
  background: white;
  font-weight: 600;
  color: #5b5b5b;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Content */
.employee-statistics-content {
  padding: 24px;
  min-height: 400px;
}

/* Task Analysis */
.employee-statistics-task-analysis {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.employee-statistics-chart-section {
  display: flex;
  flex-direction: column;
  padding: 24px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.employee-statistics-section-title {
  font-size: 18px;
  font-weight: 600;
  color: #5b5b5b;
  margin-bottom: 8px;
  align-self: flex-start;
}

.employee-statistics-section-subtitle {
  font-size: 12px;
  color: #5b5b5b;
  font-weight: 400;
  margin-bottom: 20px;
  align-self: flex-start;
}

.employee-statistics-chart-content {
  display: flex;
  align-items: center;
  gap: 32px;
}

.employee-statistics-pie-chart {
  width: 180px;
  height: 180px;
  flex-shrink: 0;
}

.employee-statistics-legend-container {
  flex: 1;
  min-width: 0;
}

.employee-statistics-legend {
  display: flex;
  flex-direction: column;
  gap: 12px;
  border: 1px solid #7C7C7C1A;
  padding: 12px;
  border-radius: 12px;
}

.employee-statistics-legend-title {
  font-size: 18px;
  font-weight: 600;
  color: #5d5d5d;
  margin-bottom: 12px;
}

.employee-statistics-legend-grid {
  display: grid;
  grid-template-columns: 2fr 1.9fr 0.9fr;
  gap: 16px;
}

.employee-statistics-legend-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.employee-statistics-legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #5b5b5b;
  font-weight: 500;
}

.employee-statistics-legend-color {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  font-weight: 600;
}

.employee-statistics-legend-label {
  font-weight: 500;
  font-size: 13px;
}

/* Projects */
.employee-statistics-projects {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.employee-statistics-projects .employee-statistics-section-title {
  font-size: 18px;
  font-weight: 600;
  color: #5b5b5b;
  margin-bottom: 8px;
  align-self: flex-start;
}

.employee-statistics-projects-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 260px;
  overflow-y: auto;
  padding-right: 8px;
  border: 1px solid transparent;
}

/* Custom scrollbar cho projects list */
.employee-statistics-projects-list::-webkit-scrollbar {
  width: 8px;
}

.employee-statistics-projects-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.employee-statistics-projects-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.employee-statistics-projects-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.employee-statistics-project-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #F0F4FF;
  border: 1px solid #E3F2FD;
  border-radius: 8px;
  transition: all 0.2s;
  height: 44px; /* Chiều cao cố định cho mỗi item */
  box-sizing: border-box;
  flex-shrink: 0; /* Đảm bảo item không bị co lại */
}

.employee-statistics-project-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  background-color: #EBF8FF;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.employee-statistics-project-icon img {
  width: 20px;
  height: 20px;
  filter: invert(60%) sepia(60%) saturate(1129%) hue-rotate(180deg) brightness(96%) contrast(87%);
}

.employee-statistics-project-name {
  font-size: 14px;
  font-weight: 500;
  color: #5b5b5b;
  flex: 1;
}

/* Responsive */
@media (max-width: 768px) {
  .employee-statistics-popup {
    margin: 10px;
    max-height: 95vh;
  }

  .employee-statistics-header {
    padding: 16px 16px 0 16px;
  }

  .employee-statistics-metrics {
    grid-template-columns: repeat(2, 1fr);
    margin: 0 16px;
    padding: 16px;
  }

  .employee-statistics-tabs {
    margin: 0 16px;
  }

  .employee-statistics-content {
    padding: 16px;
  }

  .employee-statistics-chart-section {
    flex-direction: column;
    gap: 20px;
  }

  .employee-statistics-chart-content {
    flex-direction: column;
    gap: 20px;
  }

  .employee-statistics-pie-chart {
    width: 150px;
    height: 150px;
  }

  .employee-statistics-legend-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .employee-statistics-legend-color {
    width: 16px;
    height: 16px;
    font-size: 8px;
  }
}

@media (max-width: 480px) {
  .employee-statistics-metrics {
    grid-template-columns: 1fr;
  }

  .employee-statistics-avatar-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .employee-statistics-name {
    font-size: 18px;
  }
} 